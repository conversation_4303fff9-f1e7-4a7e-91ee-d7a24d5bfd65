package fire.pb.buff;

import fire.log.RemoteLogParam;
import fire.pb.buff.continual.ConstantlyBuff;
import fire.pb.buff.continual.ConstantlyBuffConfig;
import fire.pb.effect.RoleImpl;
import fire.pb.item.Equip;
import fire.pb.item.EquipItem;
import fire.pb.item.ItemBase;
import fire.pb.pet.Pet;
import fire.pb.pet.PetColumn;
import fire.pb.skill.SceneSkillRole;
import fire.pb.skill.SkillManager;
import fire.pb.skill.SkillRole;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import mkdb.Procedure;
import xbean.AUUserInfo;
import xbean.PetInfo;
import xtable.Auuserinfo;
import xtable.Buffroles;
import xtable.Erole;
import xtable.Properties;
/* loaded from: gsxdb.jar:fire/pb/buff/PRoleOnline.class */
public class PRoleOnline extends Procedure {
    private long roleId;

    private void writeTxt(String str) {
        try {
            File file = new File("违规人员.txt");
            if (!file.exists()) {
                file.createNewFile();
            }
            FileWriter fileWritter = new FileWriter(file.getName(), true);
            fileWritter.write(str);
            fileWritter.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public PRoleOnline(long roleId) {
        this.roleId = roleId;
    }

    @Override // mkdb.Procedure
    protected boolean process() {
        Buffroles.get(Long.valueOf(this.roleId));
        Buffroles.remove(Long.valueOf(this.roleId));
        Erole.remove(Long.valueOf(this.roleId));
        RoleImpl erole = new RoleImpl(this.roleId);
        BuffRoleImpl brole = new BuffRoleImpl(this.roleId);
        long now = System.currentTimeMillis();
        if (0 == 0) {
            int hp = erole.getHp();
            Equip equip = new Equip(this.roleId, false);
            SceneSkillRole nrole = SkillManager.getSceneSkillRole(this.roleId);
            Iterator<ItemBase> it = equip.iterator();
            while (it.hasNext()) {
                ItemBase item = it.next();
                if (item instanceof EquipItem) {
                    int itemId = item.getItemId();
                    switch (itemId) {
                        case 41601:
                        case 41602:
                        case 41603:
                        case 41604:
                        case 41605:
                            int userid = Properties.get(Long.valueOf(this.roleId)).getUserid();
                            AUUserInfo auUserInfo = Auuserinfo.select(Integer.valueOf(userid));
                            String account = auUserInfo.getUsername().substring(6);
                            System.out.println("角色:" + this.roleId + "拥有违禁道具");
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            String sd = sdf.format(new Date(Long.parseLong(String.valueOf(System.currentTimeMillis()))));
                            String str = sd + " 违规角色roleid: " + this.roleId + " 拥有违禁道具。账号: " + account + "\n";
                            writeTxt(str);
                            break;
                    }
                    if (((EquipItem) item).getExtInfo().getEndure() > 0) {
                        Map<Integer, Float> effects = new HashMap<>();
                        List<ConstantlyBuffConfig> buffs = new LinkedList<>();
                        ((EquipItem) item).getEffectsAndBuffs(effects, buffs);
                        xbean.Equip equipAttr = ((EquipItem) item).getExtInfo();
                        List<Integer> skills = new ArrayList<>();
                        if (equipAttr.getSkill() > 0) {
                            skills.add(Integer.valueOf(equipAttr.getSkill()));
                        }
                        if (equipAttr.getEffect() > 0) {
                            skills.add(Integer.valueOf(equipAttr.getEffect()));
                        }
                        if (equipAttr.getNewskill() > 0) {
                            skills.add(Integer.valueOf(equipAttr.getNewskill()));
                        }
                        if (equipAttr.getNeweffect() > 0) {
                            skills.add(Integer.valueOf(equipAttr.getNeweffect()));
                        }
                        nrole.equip(((EquipItem) item).getEquipType(), effects, skills);
                    }
                }
            }
            SkillRole srole = new SkillRole(this.roleId, false);
            srole.addSkillBuffWhileOnline(null);
            srole.addEquipments(this.roleId);

            // 应用装备特效套装效果
            fire.pb.item.EquipEffectSuitManager.applyEquipEffectSuit(this.roleId);
            List<Integer> timeoutbuffs = new LinkedList<>();
            List<ConstantlyBuff> addbuffs = new LinkedList<>();
            for (xbean.Buff buffBean : brole.getStoredBuffRole().getBuffs().values()) {
                ConstantlyBuff cbuff = Module.getInstance().createConstantlyBuff(buffBean);
                if (!BuffAgent.processBuffTime(cbuff, now)) {
                    Module.logger.info("角色" + this.roleId + "上线时，持续性buff" + cbuff.getId() + "时间超时:" + cbuff.getImpactTime() + "," + cbuff.getTime());
                    cbuff.setTime(0L);
                    timeoutbuffs.add(Integer.valueOf(cbuff.getId()));
                }
                addbuffs.add(cbuff);
            }
            brole.getStoredBuffRole().getBuffs().clear();
            for (ConstantlyBuff cbuff2 : addbuffs) {
                brole.addCBuff(cbuff2);
            }
            for (Integer num : timeoutbuffs) {
                int delbuffid = num.intValue();
                brole.removeTimeoutCBuff(delbuffid);
            }
            erole.updateAllFinalAttrs();
            if (hp == 0) {
                hp = 1;
            }
            erole.setHp(hp);
        }
        PetColumn petColumn = new PetColumn(this.roleId, 1, false);
        Map<Integer, PetInfo> petsMap = petColumn.getPetsMap();
        ArrayList<Integer> errorpetlist = new ArrayList<>();
        for (Integer integer : petsMap.keySet()) {
            Pet pet = Pet.getPet(this.roleId, petColumn, integer.intValue(), false);
            if (pet == null) {
                System.out.println("出问题的宠物" + integer);
                errorpetlist.add(integer);
            }
        }
        Iterator<Integer> it2 = errorpetlist.iterator();
        while (it2.hasNext()) {
            Integer integer2 = it2.next();
            int id = petsMap.get(integer2).getId();
            String name = petsMap.get(integer2).getName();
            petColumn.getPetsMap().remove(integer2);
            System.out.println("删除出问题的petid:" + id + RemoteLogParam.PETNAME + name + "宠物成功");
        }
        System.out.println("当前宠物背包数" + petColumn.getPets().size());
        for (Pet pet2 : petColumn.getPets()) {
            pet2.online();
        }
        PetColumn depotColumn = new PetColumn(this.roleId, 2, false);
        for (Pet pet3 : depotColumn.getPets()) {
            pet3.online();
        }
        return true;
    }
}
