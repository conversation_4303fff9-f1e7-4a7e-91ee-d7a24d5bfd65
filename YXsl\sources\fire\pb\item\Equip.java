package fire.pb.item;

import fire.log.enums.YYLoggerTuJingEnum;
import fire.msp.move.GRoleEquipChange;
import fire.msp.role.GChangeEquipEffect;
import fire.pb.GsClient;
import fire.pb.battle.BattleConfig;
import fire.pb.course.CourseManager;
import fire.pb.event.EquipItemEvent;
import fire.pb.event.Poster;
import fire.pb.event.UnequipItemEvent;
import fire.pb.item.equip.diamond.EquipDiamondMgr;
import fire.pb.mission.activelist.RoleLiveness;
import fire.pb.ranklist.proc.PRoleZongheRankProc;
import fire.pb.skill.SceneSkillRole;
import fire.pb.skill.SkillManager;
import fire.pb.skill.SkillRole;
import fire.pb.talk.MessageMgr;
import fire.pb.team.Team;
import fire.pb.team.TeamManager;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import mkdb.Procedure;
import xbean.Properties;
import xtable.Roleid2teamid;
/* loaded from: gsxdb.jar:fire/pb/item/Equip.class */
public class Equip extends ItemMaps {
    public Equip(long roleId, boolean readonly) {
        super(roleId, readonly);
    }

    @Override // fire.pb.item.ItemMaps
    public int addCapacity(int size) {
        throw new UnsupportedOperationException("装备背包大小不能改变");
    }

    public void battleEnd(int battleConfigId, Map<Integer, Integer> ac) {
        BattleConfig battleConfig = fire.pb.battle.Module.getInstance().getBattleConfigs().get(Integer.valueOf(battleConfigId));
        if (battleConfig != null && battleConfig.isNotDecEndure == 1) {
            if (Module.logger.isInfoEnabled()) {
                Module.logger.info("不掉耐久, battleId=" + battleConfigId);
                return;
            }
            return;
        }
        if (Module.logger.isDebugEnabled()) {
            Module.logger.debug("耐久度降低");
        }
        SRefreshNaiJiu send = new SRefreshNaiJiu();
        send.packid = getPackid();
        for (int i = 0; i < getCapacity(); i++) {
            loseEndure(ac, i, send);
        }
        Procedure.psendWhileCommit(this.roleId, send);
        Iterator<ItemBase> it = iterator();
        while (it.hasNext()) {
            ItemBase item = it.next();
            if (item instanceof EquipItem) {
                EquipItem ei = (EquipItem) item;
                if (ei.getEndure() <= 5) {
                    ArrayList<String> params = new ArrayList<>();
                    params.add(ei.itemAttr.name);
                    MessageMgr.psendMsgNotifyWhileCommit(this.roleId, 142381, params);
                }
            }
        }
    }

    @Override // fire.pb.item.ItemMaps
    public AddItemResult doAddItem(ItemBase item, int p, String reason, YYLoggerTuJingEnum countertype, int xiangguanid) {
        int pos;
        if (p == -1) {
            if (item instanceof EquipItem) {
                EquipItem ei = (EquipItem) item;
                pos = ei.getEquipPos();
            } else {
                return AddItemResult.POS_NOT_AVAILABLE;
            }
        } else {
            pos = p;
        }
        return super.doAddItem(item, pos, reason, countertype, xiangguanid);
    }

    @Override // fire.pb.item.ItemMaps
    public int getCapacity() {
        return this.conf.sizesize;
    }

    public ItemBase getHeaddress() {
        return getItemByPos(6);
    }

    public ItemBase getHorsedress() {
        return null;
    }

    @Override // fire.pb.item.ItemMaps
    public int getPackid() {
        return 3;
    }

    public ItemBase getWeapon() {
        return getItemByPos(0);
    }

    @Override // fire.pb.item.ItemMaps
    public boolean isFull() {
        return false;
    }

    private void loseEndure(Map<Integer, Integer> ac, int pos, SRefreshNaiJiu send) {
        SEquipNaiJiuXiaoHao lose;
        Integer w;
        for (Map.Entry<Integer, Integer> e : ac.entrySet()) {
            Module.logger.debug("战斗统计消息为id=" + e.getKey() + ", 次数为 " + e.getValue());
        }
        ItemBase item = getItemByPos(pos);
        if (item == null || !(item instanceof EquipItem) || (lose = Module.getInstance().getItemManager().getLoseNaiJiu(pos)) == null) {
            return;
        }
        EquipItem eitem = (EquipItem) item;
        for (int i = 0; i < lose.yuanyin.size() - 1; i++) {
            int times = lose.cishu.get(i).intValue();
            if (times != 0 && (w = ac.get(lose.yuanyin.get(i))) != null && w.intValue() != 0) {
                Module.logger.debug("耐久消息为id=" + lose.yuanyin.get(i) + ", 次数为 " + w + "位置为" + pos);
                int before = eitem.getEquipAttr().getEndure();
                if (eitem.getEndure() > 0 && eitem.loseEndure((100 / times) * w.intValue()) && before != eitem.getEquipAttr().getEndure()) {
                    EquipNaiJiu endure = new EquipNaiJiu();
                    endure.keyinpack = item.getKey();
                    endure.endure = eitem.getExtInfo().getEndure();
                    send.data.add(endure);
                }
            }
        }
        if (eitem.getExtInfo().getEndure() == 0) {
            SceneSkillRole role = SkillManager.getSceneSkillRole(this.roleId);
            role.removeEquipEffectAndSkillWithSP(eitem);
        }
    }

    public void onUnequip(EquipItem ei) {
        GRoleEquipChange notifymap = new GRoleEquipChange();
        notifymap.roleid = this.roleId;
        notifymap.pos = ei.getEquipPos();
        notifymap.itemid = 0;
        notifymap.ride = -1;
        notifymap.effect = 0;
        GsClient.pSendWhileCommit(notifymap);
        SkillRole srole = new SkillRole(this.roleId);
        GChangeEquipEffect equipeffect = new GChangeEquipEffect();
        equipeffect.effect = 0;
        equipeffect.roleid = this.roleId;
        GsClient.pSendWhileCommit(equipeffect);
        Properties pProp = xtable.Properties.get(Long.valueOf(this.roleId));
        pProp.setEquipeffect(0);
        Long teamId = Roleid2teamid.get(Long.valueOf(this.roleId));
        if (teamId != null) {
            Procedure.pexecuteWhileCommit(new Procedure() { // from class: fire.pb.item.Equip.1
                @Override // mkdb.Procedure
                protected boolean process() throws Exception {
                    Team team = TeamManager.selectTeamByRoleId(Equip.this.roleId);
                    if (team != null) {
                        team.updateTeamMemberComponents2Others(Equip.this.roleId);
                        return true;
                    }
                    return true;
                }
            });
        }
        SAllEquipScore equipTotalScore = new SAllEquipScore();
        equipTotalScore.score = Module.getInstance().getEquipTotalScore(this.roleId);
        Procedure.psendWhileCommit(this.roleId, equipTotalScore);
        if (ei.getExtInfo().getEndure() > 0) {
            SceneSkillRole role = SkillManager.getSceneSkillRole(this.roleId);
            role.removeEquipEffectAndSkillWithSP(ei);
        }
        srole.addEquipments(this.roleId);
        Procedure.pexecuteWhileCommit(new PRoleZongheRankProc(this.roleId));

        // 应用装备特效套装效果
        EquipEffectSuitManager.applyEquipEffectSuit(this.roleId);
    }

    /* JADX INFO: Access modifiers changed from: protected */
    @Override // fire.pb.item.ItemMaps
    public boolean TransIn(ItemBase item, int pos, ItemBase dstitem) {
        if (super.TransIn(item, pos, dstitem)) {
            Poster.getPoster().dispatchEvent(new EquipItemEvent(this.roleId, item.getItemId()));
            return true;
        }
        return false;
    }

    @Override // fire.pb.item.ItemMaps
    public ItemBase TransOut(int key, int number, String reason) {
        ItemBase ret = super.TransOut(key, number, reason);
        if (ret != null) {
            Poster.getPoster().dispatchEvent(new UnequipItemEvent(this.roleId, ret.getItemId()));
        }
        return ret;
    }

    public SRefreshNaiJiu GMDecEndure(float fDecPercent) {
        SRefreshNaiJiu ret = new SRefreshNaiJiu();
        ret.packid = getPackid();
        Iterator<ItemBase> it = iterator();
        while (it.hasNext()) {
            ItemBase ib = it.next();
            if (ib != null && (ib instanceof EquipItem)) {
                EquipItem ei = (EquipItem) ib;
                ei.setEndure((int) (ei.getCurMaxEndure() * fDecPercent));
                EquipNaiJiu ee = new EquipNaiJiu();
                ee.keyinpack = ei.getKey();
                ee.endure = ei.getEndure();
                ret.data.add(ee);
            }
        }
        return ret;
    }

    public boolean isJingMaiEquip(int paramInt) {
        int[] arrayOfInt = {200, RoleLiveness.IMPEXAMSTATE, 232, 248, 264, 280, 296, 328};
        for (int i : arrayOfInt) {
            if (paramInt == i) {
                return true;
            }
        }
        return false;
    }

    public Map<Integer, ArrayList<Integer>> getEquipsGemInfo() {
        Map<Integer, ArrayList<Integer>> map = new HashMap<>();
        Iterator<ItemBase> it = iterator();
        while (it.hasNext()) {
            ItemBase ib = it.next();
            if (ib != null && (ib instanceof EquipItem)) {
                xbean.Equip equipAttr = ((EquipItem) ib).getEquipAttr();
                ArrayList<Integer> gems = new ArrayList<>(equipAttr.getDiamonds());
                if (gems.size() != 0) {
                    map.put(Integer.valueOf(ib.getItemId()), gems);
                } else {
                    map.put(Integer.valueOf(ib.getItemId()), new ArrayList<>());
                }
            }
        }
        return map;
    }

    public static void checkEquipDiamondCourse(long roleid) {
        ItemBase ei;
        Equip equipBag = (Equip) Module.getInstance().getItemMaps(roleid, 3, false);
        Integer diamondLeveMin = Integer.MAX_VALUE;
        Integer diamondNum = 0;
        Iterator<ItemBase> it = equipBag.iterator();
        while (it.hasNext() && (ei = it.next()) != null) {
            xbean.Equip equipAttrOne = ((EquipItem) ei).getEquipAttr();
            List<Integer> diamondsOne = equipAttrOne.getDiamonds();
            int dsize = diamondsOne.size();
            if (dsize == 0) {
                break;
            }
            int max = 0;
            for (Integer num : diamondsOne) {
                int diamondId = num.intValue();
                int tmp = EquipDiamondMgr.getDiamondPropMap().get(Integer.valueOf(diamondId)).level;
                if (tmp > max) {
                    max = tmp;
                }
            }
            diamondLeveMin = Integer.valueOf(max < diamondLeveMin.intValue() ? max : diamondLeveMin.intValue());
            diamondNum = Integer.valueOf(diamondNum.intValue() + 1);
        }
        if (diamondNum.intValue() == 6) {
            CourseManager.checkAchieveCourse(roleid, 46, diamondLeveMin.intValue());
            CourseManager.achieveUpdate(roleid, 38);
        }
    }
}
