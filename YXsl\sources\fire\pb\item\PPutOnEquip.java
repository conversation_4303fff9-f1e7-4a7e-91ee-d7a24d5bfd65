package fire.pb.item;

import fire.log.LogManager;
import fire.msp.move.GRoleEquipChange;
import fire.msp.role.GChangeEquipEffect;
import fire.pb.GsClient;
import fire.pb.course.CourseManager;
import fire.pb.item.EquipItem;
import fire.pb.item.equip.WeaponItem;
import fire.pb.item.equip.diamond.EquipDiamondMgr;
import fire.pb.ranklist.proc.PRoleZongheRankProc;
import fire.pb.skill.SceneSkillRole;
import fire.pb.skill.SkillManager;
import fire.pb.skill.SkillRole;
import fire.pb.talk.MessageMgr;
import fire.pb.team.Team;
import fire.pb.team.TeamManager;
import java.util.Iterator;
import java.util.List;
import mkdb.Procedure;
import xbean.Properties;
import xtable.Roleid2teamid;
/* loaded from: gsxdb.jar:fire/pb/item/PPutOnEquip.class */
public class PPutOnEquip extends Procedure {
    private final long roleId;
    private final int bagkey;
    private final int position;

    public PPutOnEquip(long roleId, int bagkey, int position) {
        this.roleId = roleId;
        this.bagkey = bagkey;
        this.position = position;
    }

    @Override // mkdb.Procedure
    protected boolean process() throws Exception {
        Pack bag = new Pack(this.roleId, false);
        Equip equip = new Equip(this.roleId, false);
        ItemBase bi = bag.getItem(this.bagkey);
        if (bi == null) {
            return false;
        }
        if (((EquipItem) bi).getEndure() == 0) {
            MessageMgr.psendMsgNotify(this.roleId, 160319, null);
            return false;
        }
        int pos = bi.getPosition();
        ItemBase bi2 = bag.TransOut(this.bagkey, -1, "穿装备");
        if (!(bi2 instanceof EquipItem)) {
            return false;
        }
        EquipItem.EquipError errorcode = canEquip(equip, (EquipItem) bi2, this.position);
        if (errorcode == EquipItem.EquipError.NO_ERROR) {
            ItemBase dstitem = equip.getItemByPos(this.position);
            ItemBase item = null;
            if (dstitem != null) {
                item = equip.TransOut(dstitem.getKey(), -1, "卸下装备");
                if (item == null || !bag.TransIn(item, pos)) {
                    return false;
                }
            }
            if (!equip.TransIn(bi2, this.position) || (bi2.getFlags() & 0) != 0) {
                return false;
            }
            if (item != null) {
                int nDstEquipLevel = ((EquipItem) item).getEquipAttr().getEquiplevel();
                int nSrcEquipLevel = ((EquipItem) bi2).getEquipAttr().getEquiplevel();
                EquipItemShuXing eiAttr = (EquipItemShuXing) item.getItemAttr();
                if (eiAttr.m228get() == 1 && ((EquipItem) item).getEquipAttr().getDiamonds().size() == 0 && nSrcEquipLevel > nDstEquipLevel) {
                    new PResolveItem(this.roleId, item.getKey()).call();
                    MessageMgr.psendMsgNotifyWhileCommit(this.roleId, 160218, (List<String>) null);
                }
            }
            if (item != null) {
                ItemBase itemEquip = equip.getItemByPos(this.position);
                xbean.Equip equipSrcAttr = ((EquipItem) item).getEquipAttr();
                xbean.Equip equipDstAttr = ((EquipItem) itemEquip).getEquipAttr();
                EquipItemShuXing eiDesAttr = (EquipItemShuXing) itemEquip.getItemAttr();
                SEquipLvGemInfo equipLvGemInfo = EquipDiamondMgr.getEquipLvGemInfoByLv(eiDesAttr.level);
                if (equipLvGemInfo == null) {
                    LogManager.logger.error("error equipLv in PPutOnEquip itemId=" + eiDesAttr.id);
                    return false;
                } else if (equipLvGemInfo.gemsLevel > 0 && equipDstAttr.getDiamonds().size() == 0) {
                    List<Integer> diamonds = equipSrcAttr.getDiamonds();
                    if (diamonds.size() > 0) {
                        SReplaceGem repGem = new SReplaceGem();
                        repGem.srckey = item.getKey();
                        repGem.deskey = itemEquip.getKey();
                        Procedure.psendWhileCommit(this.roleId, repGem);
                    }
                }
            }
            if (item != null) {
                SkillRole srole = new SkillRole(this.roleId);
                srole.removeSpecialSkillWithSP(this.position);
            }
            System.out.println("类型" + ((EquipItem) bi2).getEquipType());
            if (((EquipItem) bi2).getEquipType() == 16) {
                Procedure.pexecuteWhileCommit(new PTransformationEquip(this.roleId, bi2.getItemId(), 1));
            }
            freshEquipBuff(this.roleId, (EquipItem) bi2);
            Procedure.pexecuteWhileCommit(new PRoleZongheRankProc(this.roleId));
            CourseManager.achieveCourse(this.roleId, 22, bi2.getItemId(), 0);
            xbean.Equip equipAttrOne = ((EquipItem) bi2).getEquipAttr();
            if (equipAttrOne.getSkill() != 0) {
                EquipItemShuXing eiAttrOne = (EquipItemShuXing) bi2.getItemAttr();
                CourseManager.checkAchieveCourse(this.roleId, 40, eiAttrOne.level);
            }
            if (equipAttrOne.getEffect() != 0) {
                CourseManager.achieveUpdate(this.roleId, 33);
            }
            Equip.checkEquipDiamondCourse(this.roleId);
            Procedure.pexecuteWhileCommit(new PEnhancementTimeout(this.roleId));
            return true;
        } else if (errorcode == EquipItem.EquipError.LEVEL_NOT_SUIT) {
            MessageMgr.psendMsgNotify(this.roleId, 100065, null);
            return false;
        } else if (errorcode == EquipItem.EquipError.SCHOOL_NOT_SUIT) {
            MessageMgr.psendMsgNotify(this.roleId, 174002, null);
            return false;
        } else {
            return false;
        }
    }

    public static void freshEquipBuff(final long roleId, EquipItem ei) {
        Equip equip = new Equip(roleId, true);
        if (ei != null) {
            Integer nquality = Integer.MAX_VALUE;
            if (equip.size() == 6) {
                Iterator<ItemBase> it = equip.iterator();
                while (it.hasNext()) {
                    ItemBase item = it.next();
                    if ((item instanceof EquipItem) && item.getItemAttr().nquality < nquality.intValue()) {
                        nquality = Integer.valueOf(item.getItemAttr().nquality);
                    }
                }
                CourseManager.achieveCourse(roleId, 34, nquality.intValue(), 0);
            } else {
                nquality = 0;
            }
            Properties pProp = xtable.Properties.get(Long.valueOf(roleId));
            pProp.setEquipeffect(nquality.intValue());
            GRoleEquipChange notifymap = new GRoleEquipChange();
            notifymap.roleid = roleId;
            notifymap.pos = ei.getPosition();
            notifymap.itemid = ei.getItemId();
            notifymap.ride = -1;
            notifymap.effect = nquality.intValue();
            if (ei instanceof WeaponItem) {
                notifymap.itemcolor = ((WeaponItem) ei).getItemAttr().equipcolor;
            }
            GsClient.pSendWhileCommit(notifymap);
            if (nquality.intValue() > 0) {
                GChangeEquipEffect equipeffect = new GChangeEquipEffect();
                equipeffect.effect = nquality.intValue();
                equipeffect.roleid = roleId;
                GsClient.pSendWhileCommit(equipeffect);
            }
        }
        Long teamId = Roleid2teamid.get(Long.valueOf(roleId));
        if (teamId != null) {
            Procedure.pexecuteWhileCommit(new Procedure() { // from class: fire.pb.item.PPutOnEquip.1
                @Override // mkdb.Procedure
                protected boolean process() throws Exception {
                    Team team = TeamManager.selectTeamByRoleId(roleId);
                    if (team != null) {
                        team.updateTeamMemberComponents2Others(roleId);
                        return true;
                    }
                    return true;
                }
            });
        }
        SAllEquipScore equipTotalScore = new SAllEquipScore();
        int totalScore = Module.getInstance().getEquipTotalScore(roleId);
        equipTotalScore.score = totalScore;
        Procedure.psendWhileCommit(roleId, equipTotalScore);
        if (ei != null && ei.getExtInfo().getEndure() > 0) {
            SceneSkillRole role = SkillManager.getSceneSkillRole(roleId);
            role.addEquipEffectAndSkillWithSP(ei);
            SkillRole skillRole = new SkillRole(roleId);
            skillRole.addEquipments(roleId);
        }

        // 应用装备特效套装效果
        EquipEffectSuitManager.applyEquipEffectSuit(roleId);
    }

    private static EquipItem.EquipError canEquip(Equip equip, EquipItem item, int dstpos) {
        Properties prop = xtable.Properties.select(Long.valueOf(equip.roleId));
        int roleLevel = prop.getLevel();
        int sex = prop.getSex();
        int shape = prop.getShape();
        int school = prop.getSchool();
        EquipItem.EquipError ret = item.canEquipment(dstpos, roleLevel, sex, shape, school);
        return ret;
    }

    /* renamed from: fire.pb.item.PPutOnEquip$2  reason: invalid class name */
    /* loaded from: gsxdb.jar:fire/pb/item/PPutOnEquip$2.class */
    static class AnonymousClass2 extends Procedure {
        final /* synthetic */ long val$roleId;

        AnonymousClass2(long j) {
            this.val$roleId = j;
        }

        @Override // mkdb.Procedure
        protected boolean process() throws Exception {
            Team team = TeamManager.selectTeamByRoleId(this.val$roleId);
            if (team != null) {
                team.updateTeamMemberComponents2Others(this.val$roleId);
                return true;
            }
            return true;
        }
    }
}
