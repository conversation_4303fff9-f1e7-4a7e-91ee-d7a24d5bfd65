# 装备特效套装系统测试指南

## 🎯 现在你可以确认修改表格会在游戏中生效！

我已经为你完成了以下关键修改：

### ✅ 已完成的集成工作

1. **在装备穿戴时调用套装效果**
   - 修改了 `PPutOnEquip.java`，在装备穿戴完成后调用 `EquipEffectSuitManager.applyEquipEffectSuit(roleId)`

2. **在装备卸下时调用套装效果**
   - 修改了 `Equip.java`，在装备卸下完成后调用 `EquipEffectSuitManager.applyEquipEffectSuit(roleId)`

3. **在角色登录时应用套装效果**
   - 修改了 `PRoleOnline.java`，在角色登录时调用 `EquipEffectSuitManager.applyEquipEffectSuit(roleId)`

### 📋 配置表说明

你的配置表位置：`xlsdir/xlsdir/z装备系统相关配置表/z装备特效套装配置表.csv`

**当前配置示例：**
```csv
id,effectCount,triggerAttr,triggerSkill,uiText,priority,enable
3001,3,"101=50,102=30",0,"当前特效套装属性为3个额外触发额外+50力量+30敏捷",1,1
3002,5,"101=100,103=500",0,"当前特效套装属性为5个额外触发额外+100力量+500血量",2,1
3003,7,"101=150,103=1000,104=20",0,"当前特效套装属性为7个额外触发额外+150力量+1000血量+20速度",3,1
3004,10,"101=200,103=1500,105=50",430001,"当前特效套装属性为10个额外触发额外+200力量+1500血量+50暴击+套装技能",4,1
```

### 🔧 如何测试和修改

#### 1. 测试当前配置是否生效

**步骤：**
1. 启动游戏服务器
2. 让角色穿戴有特效的装备（确保6件套装备中有3个或以上特效）
3. 查看角色属性是否增加了对应的力量、血量等
4. 查看游戏日志，应该能看到类似这样的日志：
   ```
   应用装备特效套装效果成功, roleId=12345, effectCount=5, configId=3002
   ```

#### 2. 修改配置测试

**示例修改：**
把第一行的力量从50改成100：
```csv
3001,3,"101=100,102=30",0,"当前特效套装属性为3个额外触发额外+100力量+30敏捷",1,1
```

**测试步骤：**
1. 修改CSV文件
2. 重启服务器（或热更新配置）
3. 角色重新登录或重新穿戴装备
4. 检查属性变化

### 📊 属性ID对照表

| 属性ID | 属性名称 | 说明 |
|--------|----------|------|
| 101 | 力量 | str |
| 102 | 敏捷 | agi |
| 103 | 血量 | maxhp |
| 104 | 速度 | speed |
| 105 | 暴击 | crit |

### 🎮 工作原理

1. **特效统计**：系统会统计6件套装备（项链、腰带、靴子、衣服、武器、头盔）的特效总数
2. **配置匹配**：根据特效数量找到满足条件的最高优先级配置
3. **效果应用**：解析`triggerAttr`字符串，应用对应的属性BUFF
4. **界面显示**：显示`uiText`中的文字提示

### 🚀 你现在可以做的事情

1. **直接修改数值**：在CSV文件中修改属性加成数值
2. **添加新配置**：增加新的特效数量阶段和对应奖励
3. **修改描述文字**：更改`uiText`字段的显示内容
4. **启用/禁用**：通过`enable`字段控制某个配置是否生效
5. **调整优先级**：通过`priority`字段控制多个满足条件时的选择

### ⚠️ 注意事项

1. **重启生效**：修改配置后需要重启服务器
2. **格式正确**：确保CSV格式正确，特别是`triggerAttr`字段的格式
3. **ID唯一**：每个配置的`id`必须唯一
4. **优先级**：数字越大优先级越高

### 🎯 确认方式

**你可以通过以下方式确认修改生效：**

1. **查看角色属性面板**：数值应该有对应变化
2. **查看服务器日志**：搜索"应用装备特效套装效果成功"
3. **游戏内提示**：应该显示`uiText`中的文字
4. **BUFF列表**：角色应该有对应的套装BUFF效果

现在你可以放心地修改配置表，所有改动都会在游戏中真实生效！
