# 🎯 装备特效套装Excel配置使用说明

## 📁 Excel文件位置
```
xlsdir/xlsdir/z装备系统相关配置表/z装备特效套装配置表.xlsx
```

## 📊 标准配置表格式

| 列名 | 说明 | 示例值 | 备注 |
|------|------|--------|------|
| **id** | 配置ID | 3001 | 必须唯一，建议按顺序递增 |
| **effectCount** | 特效数量 | 3 | 需要多少个特效才触发此套装 |
| **triggerAttr** | 触发属性 | "101=100,103=800" | 属性ID=数值，多个用逗号分隔 |
| **triggerSkill** | 触发技能 | 430001 | 套装技能ID，0表示无技能 |
| **uiText** | 界面文字 | "【3特效套装】力量+100 血量+800" | 玩家看到的提示文字 |
| **priority** | 优先级 | 1 | 数字越大优先级越高 |
| **enable** | 是否启用 | 1 | 1=启用，0=禁用 |

## 🎮 当前标准配置

### 3特效套装 (ID: 3001)
- **触发条件**: 3个特效
- **属性加成**: 力量+100, 血量+800
- **技能**: 无
- **优先级**: 1

### 5特效套装 (ID: 3002)
- **触发条件**: 5个特效
- **属性加成**: 力量+200, 血量+1500, 暴击+30
- **技能**: 无
- **优先级**: 2

### 7特效套装 (ID: 3003)
- **触发条件**: 7个特效
- **属性加成**: 力量+350, 血量+2500, 暴击+60, 速度+20
- **技能**: 无
- **优先级**: 3

### 10特效套装 (ID: 3004)
- **触发条件**: 10个特效
- **属性加成**: 力量+500, 血量+4000, 暴击+100, 速度+40, 敏捷+200
- **技能**: 430001 (套装技能)
- **优先级**: 4

### 12特效套装 (ID: 3005)
- **触发条件**: 12个特效
- **属性加成**: 力量+700, 血量+6000, 暴击+150, 速度+60, 敏捷+300
- **技能**: 430002 (高级套装技能)
- **优先级**: 5

### 15特效套装 (ID: 3006)
- **触发条件**: 15个特效
- **属性加成**: 力量+1000, 血量+8000, 暴击+200, 速度+80, 敏捷+400, 防御+50
- **技能**: 430003 (顶级套装技能)
- **优先级**: 6

## 🔧 属性ID对照表

| 属性ID | 属性名称 | 英文名 | 说明 |
|--------|----------|--------|------|
| **101** | 力量 | str | 影响物理攻击力 |
| **102** | 敏捷 | agi | 影响命中和闪避 |
| **103** | 血量 | maxhp | 最大生命值 |
| **104** | 速度 | speed | 移动和攻击速度 |
| **105** | 暴击 | crit | 暴击率 |
| **106** | 防御 | def | 物理防御力 |
| **107** | 魔防 | mdef | 魔法防御力 |
| **108** | 魔攻 | matk | 魔法攻击力 |

## 📝 如何修改配置

### 1. 打开Excel文件
直接双击 `z装备特效套装配置表.xlsx` 文件

### 2. 修改数值示例
**想要把3特效套装的力量从100改成150：**
- 找到ID为3001的行
- 将 `triggerAttr` 列的值从 `"101=100,103=800"` 改成 `"101=150,103=800"`
- 将 `uiText` 列的值从 `"【3特效套装】力量+100 血量+800"` 改成 `"【3特效套装】力量+150 血量+800"`

### 3. 添加新属性示例
**想要给5特效套装增加防御+50：**
- 找到ID为3002的行
- 将 `triggerAttr` 列的值从 `"101=200,103=1500,105=30"` 改成 `"101=200,103=1500,105=30,106=50"`
- 更新 `uiText` 列的描述文字

### 4. 保存文件
按 `Ctrl+S` 保存Excel文件

## 🚀 应用修改

### 1. 替换jar文件
将修改后的Excel文件所在目录的jar文件替换到服务器

### 2. 重启服务器
重启游戏服务器使配置生效

### 3. 测试效果
- 角色重新登录
- 穿戴/卸下装备
- 查看属性变化和界面提示

## ⚠️ 重要注意事项

1. **格式要求**:
   - `triggerAttr` 必须用双引号包围
   - 多个属性用逗号分隔，格式：`"属性ID=数值,属性ID=数值"`
   - 不要有多余的空格

2. **ID唯一性**:
   - 每个配置的 `id` 必须唯一
   - 建议按3001, 3002, 3003...的顺序

3. **优先级规则**:
   - 数字越大优先级越高
   - 当角色同时满足多个条件时，选择优先级最高的

4. **特效数量**:
   - 系统会统计6件套装备的特效总数
   - 包括 `effect` 和 `neweffect` 两个字段

## 🎯 测试确认

修改后可以通过以下方式确认生效：

1. **查看角色属性面板** - 数值应该有对应变化
2. **查看游戏内提示** - 应该显示 `uiText` 中的文字
3. **查看服务器日志** - 搜索"应用装备特效套装效果成功"

现在你可以直接编辑Excel文件，保存后替换jar重启服务器就能看到效果！
